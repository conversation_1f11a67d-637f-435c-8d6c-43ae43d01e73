#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WordPress站点管理系统周报生成器
功能：基于日报内容自动生成结构化周报
作者：系统管理员
创建时间：2025-08-04
"""

import re
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import os

class 周报生成器:
    """周报自动生成核心类"""
    
    def __init__(self, 日报文件路径: str = "日报.md"):
        """
        初始化周报生成器
        
        参数:
            日报文件路径: 日报markdown文件的路径
        """
        self.日报文件路径 = 日报文件路径
        self.任务分类规则 = {
            "站点上线": r"(.+?)(上线|部署|发布)",
            "站点维护": r"(.+?)(维护|修复|处理|问题)",
            "源码打包": r"(.+?)(打包|源码|交付)",
            "安全修复": r"(.+?)(漏洞|安全|扫描|修复)",
            "性能优化": r"(.+?)(跑分|性能|优化|速度)",
            "服务器运维": r"(服务器|环境|配置|部署|nginx|php|磁盘|ssl)"
        }
        
        self.状态识别规则 = {
            "已完成": r"❇️\s*已完成",
            "进行中": r"🔛\s*进行中"
        }
        
        self.客户提取规则 = r"([\u4e00-\u9fa5]+(?:有限公司|股份有限公司|科技有限公司|电子商务有限公司|技术有限公司))"
    
    def 读取日报内容(self) -> str:
        """读取日报文件内容"""
        try:
            with open(self.日报文件路径, 'r', encoding='utf-8') as file:
                return file.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"日报文件未找到：{self.日报文件路径}")
        except Exception as e:
            raise Exception(f"读取日报文件失败：{str(e)}")
    
    def 解析日期范围(self, 开始日期: str, 结束日期: str) -> List[str]:
        """
        解析指定日期范围内的工作日
        
        参数:
            开始日期: YYYY-MM-DD格式
            结束日期: YYYY-MM-DD格式
            
        返回:
            工作日列表
        """
        start = datetime.strptime(开始日期, "%Y-%m-%d")
        end = datetime.strptime(结束日期, "%Y-%m-%d")
        
        工作日列表 = []
        current = start
        while current <= end:
            # 只包含工作日（周一到周五）
            if current.weekday() < 5:
                工作日列表.append(current.strftime("%Y年%m月%d日"))
            current += timedelta(days=1)
        
        return 工作日列表
    
    def 提取周内容(self, 日报内容: str, 工作日列表: List[str]) -> Dict:
        """
        提取指定周内的所有工作内容
        
        参数:
            日报内容: 完整的日报内容
            工作日列表: 需要提取的工作日列表
            
        返回:
            周工作数据字典
        """
        周数据 = {
            "每日工作记录": {},
            "任务统计": {
                "站点上线": {"数量": 0, "任务列表": []},
                "站点维护": {"数量": 0, "任务列表": []},
                "源码打包": {"数量": 0, "任务列表": []},
                "安全修复": {"数量": 0, "任务列表": []},
                "性能优化": {"数量": 0, "任务列表": []},
                "服务器运维": {"数量": 0, "任务列表": []}
            },
            "客户统计": {
                "客户列表": set(),
                "新客户": set(),
                "服务次数": 0
            }
        }
        
        # 按日期分割日报内容
        日期模式 = r"(\d{4}年\d{1,2}月\d{1,2}日)"
        日期分割 = re.split(日期模式, 日报内容)
        
        for i in range(1, len(日期分割), 2):
            if i + 1 < len(日期分割):
                日期 = 日期分割[i]
                内容 = 日期分割[i + 1]
                
                if 日期 in 工作日列表:
                    周数据["每日工作记录"][日期] = self.解析单日内容(内容)
                    self.统计任务分类(内容, 周数据["任务统计"])
                    self.提取客户信息(内容, 周数据["客户统计"])
        
        return 周数据
    
    def 解析单日内容(self, 日内容: str) -> Dict:
        """
        解析单日的工作内容
        
        参数:
            日内容: 单日的工作内容文本
            
        返回:
            单日工作数据
        """
        任务列表 = []
        完成任务数 = 0
        进行中任务数 = 0
        
        # 按行分割任务
        行列表 = [line.strip() for line in 日内容.split('\n') if line.strip()]
        
        for 行 in 行列表:
            if not 行:
                continue
                
            任务状态 = "已完成"  # 默认状态
            
            # 检查任务状态
            if re.search(self.状态识别规则["已完成"], 行):
                任务状态 = "已完成"
                完成任务数 += 1
            elif re.search(self.状态识别规则["进行中"], 行):
                任务状态 = "进行中"
                进行中任务数 += 1
            else:
                完成任务数 += 1  # 无标记默认为已完成
            
            # 清理任务描述（移除状态标记）
            任务描述 = re.sub(r"[❇️🔛]\s*(已完成|进行中)", "", 行).strip()
            
            if 任务描述:
                任务列表.append({
                    "描述": 任务描述,
                    "状态": 任务状态,
                    "分类": self.识别任务分类(任务描述)
                })
        
        return {
            "任务列表": 任务列表,
            "完成任务数": 完成任务数,
            "进行中任务数": 进行中任务数,
            "总任务数": len(任务列表)
        }
    
    def 识别任务分类(self, 任务描述: str) -> str:
        """
        识别任务所属分类
        
        参数:
            任务描述: 任务的文本描述
            
        返回:
            任务分类名称
        """
        for 分类名, 规则 in self.任务分类规则.items():
            if re.search(规则, 任务描述):
                return 分类名
        return "其他"
    
    def 统计任务分类(self, 日内容: str, 任务统计: Dict):
        """
        统计任务分类数量
        
        参数:
            日内容: 单日工作内容
            任务统计: 任务统计字典（会被修改）
        """
        行列表 = [line.strip() for line in 日内容.split('\n') if line.strip()]
        
        for 行 in 行列表:
            if not 行:
                continue
                
            任务描述 = re.sub(r"[❇️🔛]\s*(已完成|进行中)", "", 行).strip()
            分类 = self.识别任务分类(任务描述)
            
            if 分类 in 任务统计:
                任务统计[分类]["数量"] += 1
                任务统计[分类]["任务列表"].append(任务描述)
    
    def 提取客户信息(self, 日内容: str, 客户统计: Dict):
        """
        提取客户相关信息
        
        参数:
            日内容: 单日工作内容
            客户统计: 客户统计字典（会被修改）
        """
        # 提取公司名称
        公司匹配 = re.findall(self.客户提取规则, 日内容)
        for 公司名 in 公司匹配:
            客户统计["客户列表"].add(公司名)
            客户统计["服务次数"] += 1
    
    def 生成周报(self, 开始日期: str, 结束日期: str, 汇报人: str = "技术人员") -> str:
        """
        生成完整的周报内容
        
        参数:
            开始日期: 周开始日期 YYYY-MM-DD
            结束日期: 周结束日期 YYYY-MM-DD
            汇报人: 汇报人姓名
            
        返回:
            格式化的周报markdown内容
        """
        # 读取日报内容
        日报内容 = self.读取日报内容()
        
        # 获取工作日列表
        工作日列表 = self.解析日期范围(开始日期, 结束日期)
        
        # 提取周数据
        周数据 = self.提取周内容(日报内容, 工作日列表)
        
        # 生成周报内容
        周报内容 = self.格式化周报(周数据, 开始日期, 结束日期, 汇报人)
        
        return 周报内容
    
    def 格式化周报(self, 周数据: Dict, 开始日期: str, 结束日期: str, 汇报人: str) -> str:
        """
        将周数据格式化为markdown周报
        
        参数:
            周数据: 提取的周工作数据
            开始日期: 周开始日期
            结束日期: 周结束日期
            汇报人: 汇报人姓名
            
        返回:
            格式化的markdown周报内容
        """
        # 计算总体统计
        总任务数 = sum([分类["数量"] for 分类 in 周数据["任务统计"].values()])
        总客户数 = len(周数据["客户统计"]["客户列表"])
        
        # 读取周报模板
        with open("周报模板.md", 'r', encoding='utf-8') as f:
            模板内容 = f.read()
        
        # 替换模板变量
        周报内容 = 模板内容.format(
            开始日期=开始日期,
            结束日期=结束日期,
            姓名=汇报人,
            总工作小时数=len(周数据["每日工作记录"]) * 8,  # 假设每天8小时
            生成时间=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            下次汇报时间=(datetime.strptime(结束日期, "%Y-%m-%d") + timedelta(days=7)).strftime("%Y-%m-%d")
        )
        
        return 周报内容
    
    def 保存周报(self, 周报内容: str, 输出文件名: str = None) -> str:
        """
        保存生成的周报到文件
        
        参数:
            周报内容: 格式化的周报内容
            输出文件名: 输出文件名（可选）
            
        返回:
            保存的文件路径
        """
        if not 输出文件名:
            当前时间 = datetime.now().strftime("%Y年%m月%d日")
            输出文件名 = f"周报_{当前时间}.md"
        
        with open(输出文件名, 'w', encoding='utf-8') as f:
            f.write(周报内容)
        
        return 输出文件名

# 使用示例
if __name__ == "__main__":
    # 创建周报生成器实例
    生成器 = 周报生成器()
    
    # 生成本周周报（示例日期）
    try:
        周报内容 = 生成器.生成周报(
            开始日期="2025-07-28",
            结束日期="2025-08-01",
            汇报人="技术开发人员"
        )
        
        # 保存周报
        文件路径 = 生成器.保存周报(周报内容)
        print(f"周报已生成并保存到：{文件路径}")
        
    except Exception as e:
        print(f"生成周报时发生错误：{str(e)}")
