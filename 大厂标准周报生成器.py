#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大厂标准WordPress站点管理系统周报生成器
功能：基于日报内容生成符合大厂标准的数据驱动周报
特点：数据驱动、目标导向、问题导向、结果量化
作者：vince
创建时间：2025-08-04
"""

import re
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import os

class 大厂标准周报生成器:
    """大厂标准周报自动生成核心类"""
    
    def __init__(self, 日报文件路径: str = "日报.md", 配置文件路径: str = "周报配置.json"):
        """
        初始化大厂标准周报生成器
        
        参数:
            日报文件路径: 日报markdown文件的路径
            配置文件路径: 配置文件路径
        """
        self.日报文件路径 = 日报文件路径
        self.配置文件路径 = 配置文件路径
        self.配置 = self.加载配置()
        
        # 大厂标准的任务分类规则
        self.任务分类规则 = {
            "站点上线交付": r"(.+?)(上线|部署|发布|交付)",
            "客户问题响应": r"(.+?)(问题|故障|修复|处理|支持)",
            "系统安全维护": r"(.+?)(漏洞|安全|扫描|防护)",
            "性能优化提升": r"(.+?)(优化|跑分|性能|速度)",
            "技术创新开发": r"(.+?)(开发|脚本|工具|创新)",
            "运维保障服务": r"(服务器|环境|配置|nginx|ssl|备份)"
        }
        
        # 业务价值评估规则
        self.价值评估规则 = {
            "站点上线": {"单价": 30000, "重要性": "高"},
            "客户维护": {"单价": 5000, "重要性": "中"},
            "安全修复": {"单价": 15000, "重要性": "高"},
            "性能优化": {"单价": 8000, "重要性": "中"}
        }
    
    def 加载配置(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.配置文件路径, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.获取默认配置()
    
    def 获取默认配置(self) -> Dict:
        """获取默认配置"""
        return {
            "核心业务指标": {
                "站点上线交付": {"周目标": 5, "权重": 0.3},
                "客户问题响应": {"周目标": 10, "权重": 0.25},
                "系统稳定性": {"目标": 99, "权重": 0.25},
                "客户满意度": {"目标": 9.0, "权重": 0.2}
            }
        }
    
    def 分析工作数据(self, 周数据: Dict) -> Dict:
        """
        分析工作数据，生成大厂标准的量化指标
        
        参数:
            周数据: 提取的周工作数据
            
        返回:
            分析后的数据指标
        """
        分析结果 = {
            "核心指标": {},
            "业务价值": {},
            "效率分析": {},
            "趋势分析": {}
        }
        
        # 计算核心业务指标
        总任务数 = sum([分类["数量"] for 分类 in 周数据["任务统计"].values()])
        完成任务数 = 总任务数  # 基于日报记录习惯，大部分为已完成
        
        分析结果["核心指标"] = {
            "站点上线交付": {
                "完成数量": 周数据["任务统计"]["站点上线"]["数量"],
                "目标数量": self.配置.get("核心业务指标", {}).get("站点上线交付", {}).get("周目标", 5),
                "完成率": self.计算完成率(周数据["任务统计"]["站点上线"]["数量"], 5)
            },
            "客户问题响应": {
                "响应次数": 周数据["客户统计"]["服务次数"],
                "目标次数": 10,
                "完成率": self.计算完成率(周数据["客户统计"]["服务次数"], 10)
            },
            "工作效率": {
                "任务完成数": 完成任务数,
                "平均任务耗时": self.估算平均耗时(周数据),
                "效率评分": self.计算效率评分(完成任务数, 总任务数)
            }
        }
        
        # 计算业务价值
        分析结果["业务价值"] = self.计算业务价值(周数据)
        
        # 效率分析
        分析结果["效率分析"] = self.分析工作效率(周数据)
        
        return 分析结果
    
    def 计算完成率(self, 完成数量: int, 目标数量: int) -> float:
        """计算完成率百分比"""
        if 目标数量 == 0:
            return 0
        return round((完成数量 / 目标数量) * 100, 1)
    
    def 估算平均耗时(self, 周数据: Dict) -> float:
        """估算平均任务耗时"""
        总任务数 = sum([分类["数量"] for 分类 in 周数据["任务统计"].values()])
        if 总任务数 == 0:
            return 0
        
        # 基于任务类型估算总耗时
        总耗时 = 0
        for 分类名, 分类数据 in 周数据["任务统计"].items():
            if 分类名 == "站点上线":
                总耗时 += 分类数据["数量"] * 4  # 站点上线平均4小时
            elif 分类名 == "安全修复":
                总耗时 += 分类数据["数量"] * 3  # 安全修复平均3小时
            else:
                总耗时 += 分类数据["数量"] * 2  # 其他任务平均2小时
        
        return round(总耗时 / 总任务数, 1)
    
    def 计算效率评分(self, 完成数量: int, 总数量: int) -> int:
        """计算工作效率评分"""
        if 总数量 == 0:
            return 0
        完成率 = (完成数量 / 总数量) * 100
        
        if 完成率 >= 95:
            return 95
        elif 完成率 >= 90:
            return 90
        elif 完成率 >= 80:
            return 85
        else:
            return 75
    
    def 计算业务价值(self, 周数据: Dict) -> Dict:
        """计算业务价值贡献"""
        直接价值 = 0
        间接价值 = 0
        
        # 计算直接经济价值
        for 分类名, 分类数据 in 周数据["任务统计"].items():
            if 分类名 == "站点上线":
                直接价值 += 分类数据["数量"] * 30000  # 每个站点3万元
            elif 分类名 == "安全修复":
                直接价值 += 分类数据["数量"] * 15000  # 每次安全修复1.5万元
            elif 分类名 == "性能优化":
                直接价值 += 分类数据["数量"] * 8000   # 每次优化8千元
        
        # 计算间接价值（客户满意度、系统稳定性等）
        客户数量 = len(周数据["客户统计"]["客户列表"])
        间接价值 = 客户数量 * 5000  # 每个客户维护价值5千元
        
        return {
            "直接经济价值": 直接价值,
            "间接业务价值": 间接价值,
            "总价值": 直接价值 + 间接价值
        }
    
    def 分析工作效率(self, 周数据: Dict) -> Dict:
        """分析工作效率"""
        总任务数 = sum([分类["数量"] for 分类 in 周数据["任务统计"].values()])
        工作天数 = len(周数据["每日工作记录"])
        
        return {
            "日均任务完成": round(总任务数 / max(工作天数, 1), 1),
            "任务分布均匀度": self.计算分布均匀度(周数据),
            "工作饱和度": self.计算工作饱和度(总任务数, 工作天数)
        }
    
    def 计算分布均匀度(self, 周数据: Dict) -> float:
        """计算任务分布均匀度"""
        每日任务数 = []
        for 日期, 日数据 in 周数据["每日工作记录"].items():
            每日任务数.append(日数据["总任务数"])
        
        if not 每日任务数:
            return 0
        
        平均值 = sum(每日任务数) / len(每日任务数)
        方差 = sum([(x - 平均值) ** 2 for x in 每日任务数]) / len(每日任务数)
        
        # 均匀度评分（方差越小越均匀）
        if 方差 <= 1:
            return 95
        elif 方差 <= 4:
            return 85
        elif 方差 <= 9:
            return 75
        else:
            return 65
    
    def 计算工作饱和度(self, 总任务数: int, 工作天数: int) -> str:
        """计算工作饱和度"""
        日均任务 = 总任务数 / max(工作天数, 1)
        
        if 日均任务 >= 6:
            return "高饱和（>6任务/天）"
        elif 日均任务 >= 4:
            return "适中饱和（4-6任务/天）"
        elif 日均任务 >= 2:
            return "低饱和（2-4任务/天）"
        else:
            return "极低饱和（<2任务/天）"
    
    def 生成大厂标准周报(self, 开始日期: str, 结束日期: str, 汇报人: str = "技术人员") -> str:
        """
        生成符合大厂标准的周报
        
        参数:
            开始日期: 周开始日期 YYYY-MM-DD
            结束日期: 周结束日期 YYYY-MM-DD
            汇报人: 汇报人姓名
            
        返回:
            格式化的大厂标准周报内容
        """
        # 读取和分析数据
        from 周报生成器 import 周报生成器
        基础生成器 = 周报生成器()
        
        日报内容 = 基础生成器.读取日报内容()
        工作日列表 = 基础生成器.解析日期范围(开始日期, 结束日期)
        周数据 = 基础生成器.提取周内容(日报内容, 工作日列表)
        
        # 进行大厂标准分析
        分析数据 = self.分析工作数据(周数据)
        
        # 生成大厂标准周报
        周报内容 = self.格式化大厂周报(周数据, 分析数据, 开始日期, 结束日期, 汇报人)
        
        return 周报内容
    
    def 格式化大厂周报(self, 周数据: Dict, 分析数据: Dict, 开始日期: str, 结束日期: str, 汇报人: str) -> str:
        """
        格式化大厂标准周报
        
        参数:
            周数据: 基础周数据
            分析数据: 分析后的数据
            开始日期: 开始日期
            结束日期: 结束日期
            汇报人: 汇报人
            
        返回:
            格式化的大厂标准周报
        """
        # 读取大厂标准模板
        with open("周报模板.md", 'r', encoding='utf-8') as f:
            模板内容 = f.read()
        
        # 准备替换数据
        替换数据 = {
            "开始日期": 开始日期,
            "结束日期": 结束日期,
            "姓名": 汇报人,
            "生成时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 填充核心指标数据
        核心指标 = 分析数据["核心指标"]
        替换数据.update({
            "完成数量": 核心指标["站点上线交付"]["完成数量"],
            "目标数量": 核心指标["站点上线交付"]["目标数量"],
            "完成率": 核心指标["站点上线交付"]["完成率"],
            "响应次数": 核心指标["客户问题响应"]["响应次数"],
            "效率评分": 核心指标["工作效率"]["效率评分"]
        })
        
        # 应用模板替换
        for 键, 值 in 替换数据.items():
            模板内容 = 模板内容.replace(f"{{{键}}}", str(值))
        
        return 模板内容
    
    def 生成数据看板(self, 分析数据: Dict) -> str:
        """生成数据看板内容"""
        看板内容 = "## 📊 数据看板\n\n"
        
        # 核心指标看板
        看板内容 += "### 核心业务指标\n"
        for 指标名, 指标数据 in 分析数据["核心指标"].items():
            看板内容 += f"- **{指标名}：** {指标数据}\n"
        
        # 业务价值看板
        看板内容 += "\n### 业务价值贡献\n"
        业务价值 = 分析数据["业务价值"]
        看板内容 += f"- **总价值贡献：** {业务价值['总价值']:,}元\n"
        看板内容 += f"- **直接价值：** {业务价值['直接经济价值']:,}元\n"
        看板内容 += f"- **间接价值：** {业务价值['间接业务价值']:,}元\n"
        
        return 看板内容
    
    def 保存大厂周报(self, 周报内容: str, 开始日期: str, 结束日期: str, 汇报人: str) -> str:
        """
        保存大厂标准周报
        
        参数:
            周报内容: 周报内容
            开始日期: 开始日期
            结束日期: 结束日期
            汇报人: 汇报人
            
        返回:
            保存的文件路径
        """
        文件名 = f"大厂标准周报_{开始日期}至{结束日期}_{汇报人}.md"
        
        with open(文件名, 'w', encoding='utf-8') as f:
            f.write(周报内容)
        
        return 文件名

# 使用示例
if __name__ == "__main__":
    # 创建大厂标准周报生成器
    生成器 = 大厂标准周报生成器()
    
    try:
        # 生成大厂标准周报
        周报内容 = 生成器.生成大厂标准周报(
            开始日期="2025-07-28",
            结束日期="2025-08-01",
            汇报人="vince"
        )
        
        # 保存周报
        文件路径 = 生成器.保存大厂周报(周报内容, "2025-07-28", "2025-08-01", "vince")
        print(f"大厂标准周报已生成：{文件路径}")
        
    except Exception as e:
        print(f"生成周报时发生错误：{str(e)}")
