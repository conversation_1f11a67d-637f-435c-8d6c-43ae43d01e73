# 周报数据结构定义

## 数据提取规则

### 从日报中提取的关键信息类型

#### 1. 任务分类标识符
```markdown
站点上线类：
- 关键词：上线、部署、发布
- 示例："{公司名}上线"、"{项目名}部署"

站点维护类：
- 关键词：维护、修复、处理、优化
- 示例："{站点}问题处理"、"{功能}修复"

源码打包类：
- 关键词：打包、源码、交付
- 示例："{公司}打包"、"{项目}源码打包"

安全相关类：
- 关键词：漏洞、安全、扫描、修复
- 示例："{站点}漏洞修复"、"安全扫描"

性能优化类：
- 关键词：优化、跑分、性能、速度
- 示例："{站点}跑分优化"、"性能优化"

服务器运维类：
- 关键词：服务器、配置、环境、部署
- 示例："服务器配置"、"环境搭建"
```

#### 2. 任务状态识别
```markdown
已完成：❇️ 已完成
进行中：🔛 进行中
未标记：默认为已完成（基于日报记录习惯）
```

#### 3. 时间投入估算规则
```markdown
简单任务（账密、小修改）：0.5-1小时
中等任务（上线、配置）：2-4小时
复杂任务（开发、大型优化）：4-8小时
```

## 数据结构定义

### 周报基础数据结构
```json
{
  "周报基本信息": {
    "开始日期": "YYYY-MM-DD",
    "结束日期": "YYYY-MM-DD",
    "汇报人": "姓名",
    "总工作天数": 5,
    "总工作小时数": 40
  },
  
  "量化统计": {
    "任务分类统计": {
      "站点上线": {
        "完成数量": 0,
        "任务列表": [],
        "时间投入": 0
      },
      "站点维护": {
        "完成数量": 0,
        "任务列表": [],
        "时间投入": 0
      },
      "源码打包": {
        "完成数量": 0,
        "任务列表": [],
        "时间投入": 0
      },
      "安全修复": {
        "完成数量": 0,
        "任务列表": [],
        "时间投入": 0
      },
      "性能优化": {
        "完成数量": 0,
        "任务列表": [],
        "时间投入": 0
      },
      "服务器运维": {
        "完成数量": 0,
        "任务列表": [],
        "时间投入": 0
      }
    },
    
    "客户服务统计": {
      "新客户接入": 0,
      "客户问题响应": 0,
      "涉及客户总数": 0,
      "客户列表": []
    }
  },
  
  "每日工作记录": {
    "周一": {
      "日期": "YYYY-MM-DD",
      "任务列表": [],
      "完成任务数": 0,
      "进行中任务数": 0
    },
    "周二": {
      "日期": "YYYY-MM-DD",
      "任务列表": [],
      "完成任务数": 0,
      "进行中任务数": 0
    },
    "周三": {
      "日期": "YYYY-MM-DD",
      "任务列表": [],
      "完成任务数": 0,
      "进行中任务数": 0
    },
    "周四": {
      "日期": "YYYY-MM-DD",
      "任务列表": [],
      "完成任务数": 0,
      "进行中任务数": 0
    },
    "周五": {
      "日期": "YYYY-MM-DD",
      "任务列表": [],
      "完成任务数": 0,
      "进行中任务数": 0
    }
  },
  
  "重点项目": [
    {
      "项目名称": "",
      "项目状态": "进行中/已完成/暂停",
      "完成进度": "百分比",
      "本周完成内容": [],
      "下周计划": [],
      "涉及客户": [],
      "技术栈": []
    }
  ],
  
  "问题记录": {
    "已解决问题": [
      {
        "问题类型": "",
        "问题描述": "",
        "解决方案": "",
        "影响范围": "",
        "处理时间": ""
      }
    ],
    "待解决问题": [
      {
        "问题类型": "",
        "问题描述": "",
        "优先级": "高/中/低",
        "预计解决时间": "",
        "负责人": ""
      }
    ]
  }
}
```

## 自动化提取规则

### 任务分类正则表达式
```javascript
const 任务分类规则 = {
  站点上线: /(.+?)(上线|部署|发布)/g,
  站点维护: /(.+?)(维护|修复|处理|优化|问题)/g,
  源码打包: /(.+?)(打包|源码|交付)/g,
  安全修复: /(.+?)(漏洞|安全|扫描|修复)/g,
  性能优化: /(.+?)(跑分|性能|优化|速度)/g,
  服务器运维: /(服务器|环境|配置|部署|nginx|php)/g
};
```

### 状态识别规则
```javascript
const 状态识别规则 = {
  已完成: /❇️\s*已完成/g,
  进行中: /🔛\s*进行中/g,
  默认状态: "已完成" // 无标记的任务默认为已完成
};
```

### 客户名称提取规则
```javascript
const 客户提取规则 = {
  公司全名: /([\u4e00-\u9fa5]+(?:有限公司|股份有限公司|科技有限公司|电子商务有限公司))/g,
  简称: /([A-Za-z]+|[\u4e00-\u9fa5]{2,6}(?!有限公司))/g
};
```

## 月报数据预留接口

### 月度汇总数据结构
```json
{
  "月报汇总": {
    "月份": "YYYY-MM",
    "周报列表": [],
    "月度总计": {
      "总工作小时": 0,
      "总完成任务": 0,
      "客户服务总数": 0,
      "重大项目完成": 0
    },
    "月度趋势分析": {
      "工作效率趋势": "",
      "任务类型分布变化": "",
      "客户满意度趋势": ""
    }
  }
}
```

### 数据聚合规则
- 周报数据自动累加到月报
- 重复客户去重统计
- 项目进度跨周跟踪
- 问题解决周期统计

## 扩展性设计

### 预留字段
- 自定义标签系统
- 工作质量评分
- 客户反馈记录
- 技能提升记录

### 接口预留
- 数据导出接口
- 图表生成接口
- 邮件发送接口
- 数据分析接口
